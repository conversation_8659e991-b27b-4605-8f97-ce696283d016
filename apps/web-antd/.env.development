###
 # @Description:
 # @Autor: panmy
 # @Date: 2025-07-19 17:25:51
 # @LastEditors: panmy
 # @LastEditTime: 2025-08-01 21:44:20
###
# 端口号
VITE_PORT=5666

VITE_BASE=/

# 请求路径
VITE_BASE_URL=http://127.0.0.1:48080
# 接口地址
VITE_GLOB_API_URL=/admin-api
# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持S3服务
VITE_UPLOAD_TYPE=server
# 是否打开 devtools，true 为打开，false 为关闭
VITE_DEVTOOLS=false

# 是否注入全局loading
VITE_INJECT_APP_LOADING=true

# 默认登录用户名
VITE_APP_DEFAULT_USERNAME=admin
# 默认登录密码
VITE_APP_DEFAULT_PASSWORD=admin123

# 本地开发代理，可以解决跨域及多地址代理
# 如果接口地址匹配到，则会转发到对应的目标地址，防止本地出现跨域问题
# 可以有多个，注意多个不能换行，否则代理将会失效
# 格式：[["/api路径","目标服务器地址"]]
# VITE_PROXY = [["/admin-api","http://************:48080"]]
VITE_PROXY = [["/admin-api","http://************:48080"],["/admin-api/pay","http://************:48085"]]
